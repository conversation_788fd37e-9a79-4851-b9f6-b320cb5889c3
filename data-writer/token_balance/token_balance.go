package token_balance

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"strings"

	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/mr-tron/base58"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
)

// BalancePoint represents a balance point in time
type BalancePoint struct {
	Timestamp int64 `json:"timestamp"`
	Balance   int64 `json:"balance"`
}

// TokenBalanceConfig holds configuration for token balance calculation
type TokenBalanceConfig struct {
	FromSlot         uint64             // Starting slot number
	RPCURL           string             // RPC endpoint URL (optional, defaults to mainnet)
	CommitmentLevel  rpc.CommitmentType // Commitment level for RPC calls (optional, defaults to Finalized)
	DefaultRPCURL    string             // Default RPC URL if none provided (optional)
	ContinueOnErrors bool               // Whether to continue on non-critical errors (optional, defaults to false)
}

// TokenBalanceResult holds the result of token balance calculation
type TokenBalanceResult struct {
	BalancePoints       []BalancePoint
	TotalCurrentBalance int64
	NumIntervals        uint64
}

// GetCurrentTokenBalance gets the current token balance for a wallet and token mint
func GetCurrentTokenBalance(ctx context.Context, client *rpc.Client, wallet solana.PublicKey, tokenMint solana.PublicKey) (uint64, error) {
	// Find the token account for this wallet and mint
	tokenAccounts, err := client.GetTokenAccountsByOwner(
		ctx,
		wallet,
		&rpc.GetTokenAccountsConfig{
			Mint: &tokenMint,
		},
		&rpc.GetTokenAccountsOpts{
			Encoding: solana.EncodingBase64,
		},
	)
	if err != nil {
		return 0, fmt.Errorf("failed to get token accounts: %v", err)
	}

	if len(tokenAccounts.Value) == 0 {
		return 0, nil
	}

	// Get the token account data
	accountInfo, err := client.GetTokenAccountBalance(
		ctx,
		tokenAccounts.Value[0].Pubkey,
		rpc.CommitmentConfirmed,
	)
	if err != nil {
		return 0, fmt.Errorf("failed to get token account balance: %v", err)
	}

	return strconv.ParseUint(accountInfo.Value.Amount, 10, 64)
}

// CalculateTokenBalanceHistory calculates historical token balance points for given wallets and token mint.
func CalculateTokenBalanceHistory(ctx context.Context, tokenMint string, wallets []string, config TokenBalanceConfig) (*TokenBalanceResult, error) {
	// Validate inputs
	if tokenMint == "" {
		return nil, fmt.Errorf("token mint address is required")
	}
	if len(wallets) == 0 {
		return nil, fmt.Errorf("at least one wallet address is required")
	}
	if config.FromSlot == 0 {
		return nil, fmt.Errorf("from-slot must be greater than 0")
	}

	// Parse and validate wallet addresses
	walletBytes := make([][]byte, len(wallets))
	solanaWallets := make([]solana.PublicKey, len(wallets))
	for i, w := range wallets {
		decoded, err := base58.Decode(strings.TrimSpace(w))
		if err != nil {
			return nil, fmt.Errorf("failed to decode wallet address %s: %v", w, err)
		}
		walletBytes[i] = decoded
		solanaWallets[i] = solana.PublicKeyFromBytes(decoded)
	}

	// Parse token mint
	tokenMintBytes, err := base58.Decode(tokenMint)
	if err != nil {
		return nil, fmt.Errorf("failed to decode token mint address: %v", err)
	}
	solanaTokenMint := solana.PublicKeyFromBytes(tokenMintBytes)

	// Set up RPC URL with fallback logic
	rpcURL := config.RPCURL
	if rpcURL == "" {
		rpcURL = os.Getenv("RPC_URL")
		if rpcURL == "" {
			if config.DefaultRPCURL != "" {
				rpcURL = config.DefaultRPCURL
			} else {
				rpcURL = "https://api.mainnet-beta.solana.com"
			}
		}
	}

	// Set default commitment level if not specified
	commitmentLevel := config.CommitmentLevel
	if commitmentLevel == "" {
		commitmentLevel = rpc.CommitmentFinalized
	}

	// Initialize RPC client
	client := rpc.New(rpcURL)

	// Get current slot from RPC
	currentSlot, err := client.GetSlot(ctx, commitmentLevel)
	if err != nil {
		return nil, fmt.Errorf("failed to get current slot: %v", err)
	} else {
		fmt.Printf("Current slot: %d\n", currentSlot)
	}
	// Calculate number of intervals with more robust logic
	slotRange := currentSlot - config.FromSlot
	numIntervals := slotRange / 1500
	if slotRange%1500 != 0 {
		numIntervals++
	}
	if numIntervals == 0 {
		numIntervals = 1
	}

	// Get current balances for all wallets
	var totalCurrentBalance int64
	for _, wallet := range solanaWallets {
		balance, err := GetCurrentTokenBalance(ctx, client, wallet, solanaTokenMint)
		if err != nil {
			if config.ContinueOnErrors {
				fmt.Printf("Warning: Failed to get current balance for wallet %s: %v\n", wallet.String(), err)
				continue
			} else {
				return nil, fmt.Errorf("failed to get current balance for wallet %s: %v", wallet.String(), err)
			}
		}
		totalCurrentBalance += int64(balance)
	}

	// Generate balance points
	balancePoints := make([]BalancePoint, numIntervals)
	currentBalance := totalCurrentBalance

	for i := uint64(0); i < numIntervals; i++ {
		endSlot := currentSlot - (i * 1500)
		startSlot := endSlot - 1500 + 1
		if startSlot < config.FromSlot {
			startSlot = config.FromSlot
		}

		// Get balance changes from database
		changes, err := db.Get().GetTokenBalanceChanges(ctx, walletBytes, tokenMintBytes, startSlot, endSlot)
		if err != nil {
			return nil, fmt.Errorf("failed to get balance changes for interval %d: %v", i, err)
		}

		// Calculate balance at this point
		currentBalance -= changes

		// Get block timestamp from database
		blockTime, err := db.Get().GetSlotTimestamp(ctx, endSlot)
		if err != nil {
			if config.ContinueOnErrors {
				fmt.Printf("Warning: Failed to get block time for slot %d: %v\n", endSlot, err)
			}
			blockTimePtr, err := client.GetBlockTime(ctx, endSlot)
			if err != nil {
				fmt.Printf("failed to get block time for slot %d: %v", endSlot, err)
			}
			if blockTimePtr == nil {
				fmt.Printf("block time is nil for slot %d", endSlot)
				continue
			}
			blockTime = int64(*blockTimePtr)
			// Insert the missing slot timestamp into the database
			err = db.Get().InsertSlotTimestamp(ctx, endSlot, int64(blockTime))
			if err != nil {
				fmt.Printf("failed to insert slot timestamp for slot %d: %v", endSlot, err)
			} else {
				fmt.Printf("inserted slot timestamp for slot %d: %d\n", endSlot, blockTime)
			}
		}

		balancePoints[i] = BalancePoint{
			Timestamp: blockTime,
			Balance:   currentBalance,
		}
	}

	// Reverse the array to get chronological order
	for i, j := 0, len(balancePoints)-1; i < j; i, j = i+1, j-1 {
		balancePoints[i], balancePoints[j] = balancePoints[j], balancePoints[i]
	}

	return &TokenBalanceResult{
		BalancePoints:       balancePoints,
		TotalCurrentBalance: totalCurrentBalance,
		NumIntervals:        numIntervals,
	}, nil
}
